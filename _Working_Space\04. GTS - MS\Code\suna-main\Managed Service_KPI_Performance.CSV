#,Service Function,PI,Mandatory for PI Report,PI Target,Reporting Period,PI Category
Managed Services,,,,,,
PI001,Reporting obligations / Governance,R_Incident_P1: % of RCA reports for  (P1)  incidents.,Yes,"Initial RCA draft: 100% within 2 working days 
Final RCA: 100% within 5 working days ",Monthly,B
PI002,Reporting obligations / Governance,R_Weekly reports: % of agreed Weekly reports issued in quality and in time,Yes,100% for agreed reports,Monthly,B
PI003,Reporting obligations / Governance,R_Daily reports: % of agreed daily reports issued in quality and in time,Yes,100% within agreed time frame.,Monthly,B
PI004,Reporting obligations / Governance,R_OnDemand_Reports & MoMs: % of On-Demand report (e.g. Special Event / Ad-hoc reports /specific analysis etc.)  ,Yes,100% within mutually agreed time frame,Monthly,B
PI005,Services Performance and Availability,"OM End to End Services Unavailability Ratio 
(according to Orange E2E measurements put in place)",Yes,Pourcentage ,Monthly,C
PI006,Change Management,Response time for Change request evaluation ,Yes,"100%
for urgent demands such as security or regulation: High level estimates within 1 working days. Detailed estimates within 3 working days.
delay is depending on local obligations.",Monthly,B
PI007,Change Management,Volumetry of Change performed,Yes,volumetry,Monthly,C
PI008,Change Management,"Nb of ""standard change"" performed during the period, with unexpected impact on Service, and thus triggering an incident (Standard change=change supposed to be without impact on Service)",Yes,volumetry,Monthly,C
PI009,Change Management,"P_Change_Success = % of change performed entirely and successfully during the period (expressed in %) and performed in respect of the agreed planning (concerns Standard changes as well as Normal, Major change) 
Note: P_Change_Error (%) + P_Change_Success (%) + P_Change_Failed (%) = 100%",Yes,95.00%,Monthly,B
PI010,Change Management,"Changes generating incidents
Nb of changes generating an incident/ total number of changes",Yes,1.00%,Monthly,B
PI011,Change Management,"P_Change_Failed = % of Change not performed ""entirely"" as planned or with a requirement to roll-back (due lack of window time, non respect of planning, technical issue, postpone of the change execution ��etc)  
Note: P_Change_Error (%) + P_Change_Success (%) + P_Change_Failed (%) = 100%",Yes,1.00%,Monthly,B
PI012,Change Management,"Average Time (hh:mm) needed to analyse and plan/schedule a Major Change involving the MSP (from the moment the request is issued by the requester (ticket in Swan) to its successful validation by Change Manager). It concerns ""Major"" changes going via CAB. The time for CAB procedure and validation is counted.",Yes,volumetry,Monthly,C
PI013,Change Management,% of maximum rollbacks per Affiliate per year ,Yes,Maximum 4%,Monthly,B
PI014,Special Events Management,Number of special events monitoring supported in a year per affiliate,Yes,volumetry,Annually,C
PI015,Special Events Management,Duration of special event monitoring,Yes,Maximum 1 month per event monitoring (i.e. religiouse events),Annually,C
PI016,Special Events Management,"Special Events : On-time Readiness rate
Proportion of each preparation activity completed without undue delay with the required complete calendar date.
Number of on-time completed Days/Hours/Number of overall Scheduled Days/Hours  x 100%",Yes,percentage,Annually,C
PI017,Problem Management,Number of problems ongoing / in the pipe,Yes,volumetry,Monthly,C
PI018,Problem Management,Percentage of problems not closed/ongoing that are out of SLA,Yes,1.00%,Monthly,B
PI019,Problem Management,Average LiveTime of problem tickets still opened / in the pipe at the end of observation period (dd:hh),Yes,value (dd:hh),Monthly,C
PI020,Problem Management,Number of problems initiated during the period of observation (n),Yes,volumetry,Monthly,C
PI021,Problem Management,Number of problems closed during the period of observation  (ticket closed with a final solution) (n),Yes,volumetry,Monthly,C
PI022,Problem Management,Resolution time for problem priority P1/P2/P3/P4 (between the problem ticket creation and the ticket closed),Yes,as per agreed Orange proposal in HLD,Monthly,B
PI023,Supervision/Monitoring,Number (n) of incidents as P1 / P2 / P3 / P4 tickets issued by the Supervision within the period (volumetry),Yes,as per details in HLD / LLD or otherwise agreed locally,Monthly,C
PI024,Supervision/Monitoring,"SUP_missed_P1 / P2 / P3 / P4_Incidents (n) = Number of incidents as tickets notification received by Huawei from the Orange SMC (or other sources, BO, QoS Teams ...)  within the period (volumetry) = Incident not seen (missed or impact Service not observed) by the Supervision",Yes,volumetry,Monthly,C
PI025,Supervision/Monitoring,Max initial response time for P1/P2/P3/P4,Yes,as per details in HLD / LLDor otherwise agreed locally,Monthly,B
PI026,Supervision/Monitoring,"Percentage of critical/major (P1/P2) issues not generating an alarm in the monitoring tools

the PI definition and value is linked to continuous improvement (evaluated with a yearly benchmark)
",Yes,"After a 1st quarter of observation, the value will be defined between Huawei and the affiliate.",Monthly,B
PI027,Incident Management,% of P1 incidents restored within the agreed SLA ,Yes,80.00%,Monthly,B
PI028,Incident Management,% of P2 incidents restored within the agreed SLA ,Yes,85.00%,Monthly,B
PI029,Incident Management,% of P3 incidents restored within the agreed SLA ,Yes,95.00%,Monthly,B
PI030,Incident Management,% of P4 incidents restored within the agreed SLA ,Yes,85.00%,Monthly,B
PI031,Incident Management,No of incidents Restored but not Resolved by priority,Yes,volumetry,Monthly,C
PI032,Incident Management,No of tickets -  Resolution Time  for P1 (whatever the source of incident),Yes,volumetry,Monthly,C
PI033,Incident Management,No of tickets -  Resolution Time  for P2 (whatever the source of incident),Yes,volumetry,Monthly,C
PI034,Incident Management,No of tickets -  Resolution Time  for P3 (whatever the source of incident),Yes,volumetry,Monthly,C
PI035,Incident Management,No of tickets -  Resolution Time  for P4 (whatever the source of incident),Yes,volumetry,Monthly,C
PI036,Incident Management,Periodicity of analysis update for P1,Yes,Every 30 minutes (24/7 *),Monthly,B
PI037,Incident Management,Periodicity of analysis update for P2,Yes,Every 45 minutes,Monthly,B
PI038,Incident Management,Periodicity of analysis update for P3,Yes,every 8 business hours,Monthly,B
PI039,Incident Management,Periodicity of analysis update for P4,Yes,every 24 business hours,Monthly,B
PI040,Incident Management,"RCA availability  (%, RCA provided for P1 and P2)",Yes,100% of the RCA available within 2 working days,Monthly,B
PI041,Release Management,"P_Rel_Ontime_Release (%) 
Ratio of Deployment performed in agreed timeframe (Features, HW, Software ��)",Yes,95.00%,Monthly,B
PI042,Release Management,"Max % of delay of a project related to the initial time planned, due to Huawei",Yes,Maximum 5%,Monthly,B
PI043,Release Management,Number of incidents  (critical or major) caused on country A by a change done on country B in a country hub architecture,Yes,0.00%,Monthly,B
PI044,Infra  management,Remedy for critical and major severity (failures),Yes,2 calendar days,Monthly,B
PI045,Infra  management,Remedy for medium and low severity (failures),Yes,3 working days,Monthly,B
PI046,MS Tools Usage,"T_Oc��ane_Ressource_not_referenced(%) = % of tickets opened by the Huawei and not created in Oceane due to the fact that ""Resource"" is not referenced",Yes,"5%
",Monthly,B
PI047,MS Tools Usage,T_Oc��ane_P1_Ressource_not_referenced (n),Yes,volumetry,Monthly,C
PI048,MS Tools Usage,T_Oc��ane_P2_Ressource_not_referenced(n),Yes,volumetry,Monthly,C
PI049,MS Tools Usage,T_Oc��ane_P3_Ressource_not_referenced(n),Yes,volumetry,Monthly,C
PI050,MS Tools Usage,T_Oc��ane_P4_Ressource_not_referenced(n),Yes,volumetry,Monthly,C
PI051,MS Tools Usage,"T_Oc��ane_Not_Closed (n)
number of all tickets incident ""Resource""  (all priorities)  opened by the Huawei not closed yet",Yes,volumetry,Monthly,C
PI052,MS Tools Usage,T_Oc��ane_P1_Not_Closed (n),Yes,volumetry,Monthly,C
PI053,MS Tools Usage,T_Oc��ane_P2_Not_Closed (n),Yes,volumetry,Monthly,C
PI054,MS Tools Usage,T_Oc��ane_P3_Not_Closed (n),Yes,volumetry,Monthly,C
PI055,MS Tools Usage,T_Oc��ane_P4_Not_Closed (n),Yes,volumetry,Monthly,C
PI056,MS Tools Usage,"T_Oc��ane_Root_Cause_filled (%)
Ratio of Oceane TT with proper Root Cause information filled  (for P1- P2 incident tickets closed where the SLA has been reached)",Yes,100%,Monthly,B
PI057,MS Tools Usage,"T_Swan_OutOf_Catalog(n)
Nb of change initiated by the Huawei and/or the affiliate not in SWAN due to absence in the Change catalog",Yes,volumetry,Monthly,C
PI058,MS Tools Usage,T_Swan_not_closed(n),Yes,volumetry,Monthly,C
PI059,MS Tools Usage,T_Swan_Standard_not_closed(n),Yes,volumetry,Monthly,C
PI060,MS Tools Usage,T_Swan_Normal_not_closed(n),Yes,volumetry,Monthly,C
PI061,MS Tools Usage,T_Swan_Major_not_closed(n),Yes,volumetry,Monthly,C
PI062,Infrastructure and platform management,The maximum time to patch the infrastructure and platform,Yes,45 days,Quarterly,B
PI063,Preventive Maintenance,Frequency of preventive maintenance checks (e.g. platform health checks) report,Yes,Daily (on working days),Daily (Working Days),B
PI064,Spare Parts,"breach due to delay in spare parts delivery to required spot   
  as per formula agreed in HLD
?	Release Request Time: Time of release completed and Release requested
?	CR Received Time: Time of CR for release received
?	Total # of release: Total number of release
Measure: time (e.g. minutes, hours).",Yes,3%,Monthly,B
PI065,Transaction Monitoring,% of fraudulent transactions flagged,Yes,100% detected within 24 hours,Daily,B
PI066,System Security,% of unauthorized system access attempts,Yes,100% blocked in real-time,Real-time,B
PI067,Data Integrity,% of errors in transaction logs,Yes,< 0.02% of total transactions,Monthly,B
PI068,Reporting Compliance,% of reports submitted with inconsistent data,Yes,< 5% of total reports,Monthly,B
PI069,Audit & Finance,% of financial discrepancies in audit reports,Yes,100% reconciled within 5 days,Quarterly,B
PI070,Transaction Monitoring,% of suspension instructions executed,Yes,100% executed,Daily,B
